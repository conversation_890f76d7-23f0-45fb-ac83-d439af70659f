# Generated by Django 5.2 on 2025-05-07 11:37

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StandardType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('is_deleted', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Standard',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('standard_title', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('version', models.CharField(max_length=50)),
                ('is_deleted', models.<PERSON>olean<PERSON>ield(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('generated_content', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='standards', to='api.generatedcontent')),
                ('standard_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='standards', to='api.standardtype')),
            ],
        ),
    ]
