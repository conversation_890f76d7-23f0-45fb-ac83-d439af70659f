name: Build and Deploy Django App via Docker to Azure

on:
  push:
    branches:
      - main  # or your deployment branch

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    env:
      AZURE_WEBAPP_NAME: mmc-backend # Your Azure App Service for containers name
      ACR_LOGIN_SERVER: ${{ secrets.ACR_LOGIN_SERVER }} # e.g., youracrname.azurecr.io
      IMAGE_NAME: medical-app # Your image name

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Azure Container Registry (ACR)
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ACR_LOGIN_SERVER }} # Use env var for consistency
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Build and Push Docker Image to ACR
        uses: docker/build-push-action@v5
        with:
          context: . # Assumes Dockerfile is at the root of the repository
          # file: ./Dockerfile # Specify if <PERSON><PERSON><PERSON><PERSON> is not at context root
          push: true
          tags: ${{ env.ACR_LOGIN_SERVER }}/${{ env.IMAGE_NAME }}:latest # Using env vars
          build-args: |
            DJANGO_SECRET_KEY_BUILD=${{ secrets.DJANGO_BUILD_TIME_SECRET_KEY }}
            DJANGO_DEBUG_BUILD=False
            DJANGO_DATABASE_URL_BUILD=sqlite:///:memory:
            # If you added more ARGs in Dockerfile for collectstatic, pass them here:
            # DJANGO_STATIC_ROOT_BUILD=/app/staticfiles
        # Note: DJANGO_BUILD_TIME_SECRET_KEY should be a GitHub secret with a dummy value.
        # It does NOT need to be your production SECRET_KEY.

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }} # Your Azure Service Principal credentials

      - name: Deploy to Azure Web App for Containers
        uses: azure/webapps-deploy@v2
        with:
          app-name: ${{ env.AZURE_WEBAPP_NAME }}
          images: ${{ env.ACR_LOGIN_SERVER }}/${{ env.IMAGE_NAME }}:latest # Using env vars
          # Azure App Service Application Settings will provide the RUNTIME environment variables
