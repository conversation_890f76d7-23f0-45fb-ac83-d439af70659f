# Generated by Django 5.2 on 2025-05-04 10:57

import django.db.models.deletion
import pgvector.django.vector
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file_name', models.Char<PERSON>ield(max_length=255)),
                ('document_type', models.CharField(help_text='e.g., Policy, Best Practice, Procedure, Standing Order', max_length=100)),
                ('supabase_storage_path', models.CharField(max_length=1024)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('metadata', models.J<PERSON>NField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='GeneratedContent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('topic', models.CharField(max_length=255)),
                ('content_type', models.Char<PERSON>ield(max_length=100)),
                ('generated_text', models.TextField()),
                ('llm_model_used', models.CharField(max_length=200)),
                ('source_chunk_ids', models.JSONField(blank=True, default=list, null=True)),
                ('validation_results', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='DocumentChunk',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('chunk_text', models.TextField()),
                ('embedding', pgvector.django.vector.VectorField(dimensions=384)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chunks', to='api.document')),
            ],
        ),
    ]
