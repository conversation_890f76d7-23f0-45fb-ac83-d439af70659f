# Generated by Django 4.2.21 on 2025-05-21 13:49

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('api', 'migration_fix'),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditQuestion',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('question_text', models.TextField()),
                ('policy_name', models.Char<PERSON>ield(max_length=255)),
                ('ai_model', models.Char<PERSON>ield(max_length=200)),
                ('options', models.<PERSON><PERSON><PERSON>ield(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='QuestionOption',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('label', models.Char<PERSON>ield(max_length=100)),
                ('property', models.Char<PERSON>ield(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
