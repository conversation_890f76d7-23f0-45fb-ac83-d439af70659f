# Environment variables - comprehensive
.env
**/.env
.env.*
**/.env.*
.env.local
.env.development
.env.test
.env.production
.env.backup
.env.*.local
env.bak/
env/
.venv
venv/
ENV/

# Python bytecode and cache
__pycache__/
**/__pycache__/
*/__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment - comprehensive
pyvenv.cfg
pip-log.txt
pip-delete-this-directory.txt
.python-version
virtualenv/
.venv/
**/.venv/
**/.venv/**
.venv/**
Scripts/
Lib/
Include/
share/
etc/

# Cache files
.cache/
**/.cache/
.pytest_cache/
**/.pytest_cache/
.coverage
.mypy_cache/
**/.mypy_cache/
.ruff_cache/
**/.ruff_cache/
.hypothesis/
**/.hypothesis/
.tox/
.nox/
__pypackages__/
.dmypy.json
dmypy.json
.pyre/
.pytype/
cython_debug/
.ipynb_checkpoints/
**/.ipynb_checkpoints/
**/.cache
**/.cache/**
**/.vector_cache/
**/.vector_cache/**
.sass-cache/
node_modules/
.npm/
.eslintcache
.stylelintcache
.node_repl_history

# Flask
instance/
.webassets-cache
flask_session/

# Database
*.db
*.sqlite
*.sqlite3
*.db-shm
*.db-wal

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Uploaded files
uploads/
media/
static/uploads/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.sublime-project
*.sublime-workspace
.project
.classpath
.settings/
.history/
*.code-workspace
.vscode-test
.netbeans.xml
nbproject/
.buildpath
.externalToolBuilders/
.launch
.cproject
.factorypath
.apt_generated/
.apt_generated_test/
.sts4-cache/
.jdt/
.metadata/
.recommenders/
.sonarlint/

# Model files and caches
*.pt
*.pth
*.bin
*.onnx
*.h5
*.pkl
*.model
*.joblib
*.tfevents.*
.vector_cache/
.huggingface/
.torch/
.keras/
.tensorflow/
.transformers/
.spacy/
.nltk_data/

# Jupyter Notebook
.ipynb_checkpoints
*/.ipynb_checkpoints/*
profile_default/
ipython_config.py

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.nox/
.coverage.*
.cache
coverage/

# Documentation
docs/_build/
site/
_site/
.jekyll-cache/
.jekyll-metadata
.docusaurus

# Large files
*.csv
*.parquet
*.pkl
*.json
*.npy
*.npz
*.zip
*.tar
*.tar.gz
*.tgz
*.rar
*.7z
*.pdf
*.docx
*.xlsx
*.pptx

__pycache__
*.pyc
*.pyo
*.pyd
env/
venv/
*.db
*.sqlite3
.git
